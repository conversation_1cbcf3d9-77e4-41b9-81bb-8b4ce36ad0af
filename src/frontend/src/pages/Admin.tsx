import { UserManagement } from '@descope/react-sdk';
import { getCurrentTenant, getSessionToken } from '@descope/react-sdk';

export default function Admin() {
  const tenantId = getCurrentTenant(getSessionToken());

  // Add role-management-widget and audit widget with basic navigation to choose between them

  return <UserManagement
    widgetId="user-management-widget"
    tenant={tenantId}
  />;
}