import React from 'react';
import { Heading } from '../components/ui/catalyst/heading';
import { Text } from '../components/ui/catalyst/text';
import { Button } from '../components/ui/catalyst/button';
import { Card } from '../components/ui/card';
import { BoltIcon } from '@heroicons/react/24/outline';

export default function WorkflowList() {
  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Heading level={1}>Workflows</Heading>
          <Text className="text-zinc-500 dark:text-zinc-400 text-lg">
            Manage and monitor your automated workflows
          </Text>
        </div>
        <Button
          className="inline-flex items-center space-x-2 shadow-soft hover:shadow-medium transition-shadow"
          color="green"
        >
          <BoltIcon className="h-4 w-4" />
          <span>Create Workflow</span>
        </Button>
      </div>

      {/* Coming Soon Card */}
      <Card
        className="text-center animate-slide-up"
        padding="lg"
        shadow="medium"
      >
        <div className="space-y-4">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900">
            <BoltIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
          </div>
          <div className="space-y-2">
            <Heading level={3}>Coming Soon</Heading>
            <Text color="secondary">
              Workflow management interface will be implemented in Phase 1B
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
}
