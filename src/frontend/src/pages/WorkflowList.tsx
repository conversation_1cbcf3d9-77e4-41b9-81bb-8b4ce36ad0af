import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Heading } from '../components/ui/catalyst/heading';
import { Text } from '../components/ui/catalyst/text';
import { Button } from '../components/ui/catalyst/button';
import { Input } from '../components/ui/catalyst/input';
import { Listbox, ListboxOption } from '../components/ui/catalyst/listbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/catalyst/table';
import { Badge } from '../components/ui/catalyst/badge';
import { WorkflowCard } from '../components/workflows/WorkflowCard';
import { WorkflowForm } from '../components/workflows/WorkflowForm';
import { useWorkflows } from '../hooks/useWorkflows';
import type { Workflow, WorkflowStatus } from '../types/api';
import { ROUTES } from '../routes/routes';
import {
  BoltIcon,
  MagnifyingGlassIcon,
  Squares2X2Icon,
  TableCellsIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlayIcon,
  PauseIcon,
  PencilIcon,
  DocumentDuplicateIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';

type ViewMode = 'grid' | 'table';

const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

const statusConfig = {
  active: { color: 'green' as const, label: 'Active' },
  inactive: { color: 'zinc' as const, label: 'Inactive' },
};

export default function WorkflowList() {
  const navigate = useNavigate();
  const {
    workflows,
    loading,
    error,
    filters,
    sortConfig,
    setFilters,
    setSortConfig,
    createWorkflow,
    updateWorkflow,
    toggleWorkflowStatus,
    duplicateWorkflow,
    deleteWorkflow,
  } = useWorkflows();

  // Initialize view mode from localStorage or default to table
  const [viewMode, setViewMode] = useState<ViewMode>(() => {
    const savedViewMode = localStorage.getItem('workflowViewMode');
    return (savedViewMode as ViewMode) || 'table';
  });
  const [formState, setFormState] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'duplicate';
    workflow: Workflow | null;
  }>({
    isOpen: false,
    mode: 'create',
    workflow: null,
  });

  const handleCreateWorkflow = () => {
    setFormState({
      isOpen: true,
      mode: 'create',
      workflow: null,
    });
  };

  const handleWorkflowView = (workflow: Workflow) => {
    navigate(ROUTES.WORKFLOW_DETAIL(workflow.id));
  };

  const handleWorkflowEdit = (workflow: Workflow) => {
    setFormState({
      isOpen: true,
      mode: 'edit',
      workflow,
    });
  };

  const handleWorkflowDuplicate = async (workflow: Workflow) => {
    setFormState({
      isOpen: true,
      mode: 'duplicate',
      workflow,
    });
  };

  const handleWorkflowDelete = async (workflow: Workflow) => {
    if (window.confirm(`Are you sure you want to delete "${workflow.name}"?`)) {
      try {
        await deleteWorkflow(workflow.id);
      } catch (error) {
        console.error('Failed to delete workflow:', error);
      }
    }
  };

  const handleToggleStatus = async (workflow: Workflow) => {
    try {
      await toggleWorkflowStatus(workflow.id);
    } catch (error) {
      console.error('Failed to toggle workflow status:', error);
    }
  };

  const handleSort = (field: keyof Workflow) => {
    setSortConfig({
      field,
      direction: sortConfig.field === field && sortConfig.direction === 'asc' ? 'desc' : 'asc',
    });
  };

  const handleViewModeChange = (newViewMode: ViewMode) => {
    setViewMode(newViewMode);
    localStorage.setItem('workflowViewMode', newViewMode);
  };

  const getSortIcon = (field: keyof Workflow) => {
    if (sortConfig.field !== field) return null;
    return sortConfig.direction === 'asc' ? (
      <ArrowUpIcon className="w-4 h-4" />
    ) : (
      <ArrowDownIcon className="w-4 h-4" />
    );
  };

  const handleFormSubmit = async (workflowData: Partial<Workflow>) => {
    try {
      if (formState.mode === 'create') {
        await createWorkflow(workflowData as Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>);
      } else if (formState.mode === 'edit' && formState.workflow) {
        await updateWorkflow(formState.workflow.id, workflowData);
      } else if (formState.mode === 'duplicate') {
        await duplicateWorkflow(formState.workflow!.id);
      }
    } catch (error) {
      console.error('Failed to save workflow:', error);
      throw error; // Re-throw to let the form handle the error
    }
  };

  const handleFormClose = () => {
    setFormState({
      isOpen: false,
      mode: 'create',
      workflow: null,
    });
  };

  if (error) {
    return (
      <div className="space-y-6">
        <Heading level={1}>Workflows</Heading>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <Text className="text-red-700">{error}</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Heading level={1}>Workflows</Heading>
          <Text className="text-zinc-500 dark:text-zinc-400 text-lg">
            Manage and monitor your automated workflows
          </Text>
        </div>
        <Button
          onClick={handleCreateWorkflow}
          className="inline-flex items-center space-x-2 shadow-soft hover:shadow-medium transition-shadow"
          color="green"
        >
          <BoltIcon className="h-4 w-4" />
          <span>Create Workflow</span>
        </Button>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-zinc-400" />
            <Input
              type="text"
              placeholder="Search workflows..."
              value={filters.search || ''}
              onChange={(e) => setFilters({ search: e.target.value })}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <div className="min-w-[180px]">
            <Listbox
              value={filters.status || 'all'}
              onChange={(value) => setFilters({ status: value as WorkflowStatus | 'all' })}
            >
              {statusOptions.map((option) => (
                <ListboxOption key={option.value} value={option.value}>
                  {option.label}
                </ListboxOption>
              ))}
            </Listbox>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2 bg-zinc-100 dark:bg-zinc-800 rounded-lg p-1">
          <button
            onClick={() => handleViewModeChange('grid')}
            className={clsx(
              'p-2 rounded-md transition-colors',
              viewMode === 'grid'
                ? 'bg-white dark:bg-zinc-700 shadow-sm'
                : 'hover:bg-zinc-200 dark:hover:bg-zinc-700'
            )}
          >
            <Squares2X2Icon className="w-4 h-4" />
          </button>
          <button
            onClick={() => handleViewModeChange('table')}
            className={clsx(
              'p-2 rounded-md transition-colors',
              viewMode === 'table'
                ? 'bg-white dark:bg-zinc-700 shadow-sm'
                : 'hover:bg-zinc-200 dark:hover:bg-zinc-700'
            )}
          >
            <TableCellsIcon className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      )}

      {/* Content */}
      {!loading && (
        <>
          {workflows.length === 0 ? (
            <div className="text-center py-12">
              <BoltIcon className="mx-auto h-12 w-12 text-zinc-400" />
              <Heading level={3} className="mt-4">No workflows found</Heading>
              <Text className="mt-2 text-zinc-500">
                {filters.search || filters.status !== 'all'
                  ? 'Try adjusting your filters to see more results.'
                  : 'Get started by creating your first workflow.'}
              </Text>
              {(!filters.search && filters.status === 'all') && (
                <Button
                  onClick={handleCreateWorkflow}
                  className="mt-4"
                  color="green"
                >
                  Create Your First Workflow
                </Button>
              )}
            </div>
          ) : (
            <>
              {/* Grid View */}
              {viewMode === 'grid' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {workflows.map((workflow) => (
                    <WorkflowCard
                      key={workflow.id}
                      workflow={workflow}
                      onView={handleWorkflowView}
                      onEdit={handleWorkflowEdit}
                      onDuplicate={handleWorkflowDuplicate}
                      onDelete={handleWorkflowDelete}
                      onToggleStatus={handleToggleStatus}
                    />
                  ))}
                </div>
              )}

              {/* Table View */}
              {viewMode === 'table' && (
                <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-700">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableHeader
                          className="cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800"
                          onClick={() => handleSort('name')}
                        >
                          <div className="flex items-center space-x-2  className='pl-2'">
                            <span>Name</span>
                            {getSortIcon('name')}
                          </div>
                        </TableHeader>
                        <TableHeader>Status</TableHeader>
                        <TableHeader
                          className="cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800"
                          onClick={() => handleSort('totalOpportunities')}
                        >
                          <div className="flex items-center space-x-2">
                            <span>Opportunities</span>
                            {getSortIcon('totalOpportunities')}
                          </div>
                        </TableHeader>
                        <TableHeader
                          className="cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800"
                          onClick={() => handleSort('averageARR')}
                        >
                          <div className="flex items-center space-x-2">
                            <span>Average ARR</span>
                            {getSortIcon('averageARR')}
                          </div>
                        </TableHeader>
                        <TableHeader
                          className="cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800"
                          onClick={() => handleSort('closedPercentage')}
                        >
                          <div className="flex items-center space-x-2">
                            <span>Closed %</span>
                            {getSortIcon('closedPercentage')}
                          </div>
                        </TableHeader>
                        <TableHeader>Tags</TableHeader>
                        <TableHeader>Actions</TableHeader>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {workflows.map((workflow) => {
                        const statusInfo = statusConfig[workflow.status];
                        const formatCurrency = (amount: number) => {
                          if (amount === 0) return '$0';
                          if (amount >= 1000000) return `$${(amount / 1000000).toFixed(1)}M`;
                          if (amount >= 1000) return `$${(amount / 1000).toFixed(0)}K`;
                          return `$${amount.toLocaleString()}`;
                        };

                        return (
                          <TableRow
                            key={workflow.id}
                            className="cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800"
                            onClick={() => handleWorkflowView(workflow)}
                          >
                            <TableCell>
                              <div className='pl-2'>
                                <Text className="font-medium">{workflow.name}</Text>
                                <Text className="text-sm text-zinc-500">
                                  v{workflow.version} • {workflow.owner?.name}
                                </Text>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge color={statusInfo.color}>
                                {statusInfo.label}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Text className="text-blue-600 dark:text-blue-400">
                                {workflow.totalOpportunities || 0}
                              </Text>
                            </TableCell>
                            <TableCell>
                              <Text className="text-green-600 dark:text-green-400 font-medium">
                                {formatCurrency(workflow.averageARR || 0)}
                              </Text>
                            </TableCell>
                            <TableCell>
                              <Text className={
                                (workflow.closedPercentage || 0) >= 60
                                  ? 'text-green-600 dark:text-green-400'
                                  : (workflow.closedPercentage || 0) >= 40
                                    ? 'text-yellow-600 dark:text-yellow-400'
                                    : 'text-red-600 dark:text-red-400'
                              }>
                                {workflow.closedPercentage || 0}%
                              </Text>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {workflow.tags?.slice(0, 2).map((tag) => (
                                  <Badge key={tag} color="zinc" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                                {workflow.tags && workflow.tags.length > 2 && (
                                  <Badge color="zinc" className="text-xs">
                                    +{workflow.tags.length - 2}
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-1">
                                <Button
                                  className="p-1.5"
                                  color={workflow.status === 'active' ? 'red' : 'green'}
                                  onClick={(e: React.MouseEvent) => {
                                    e.stopPropagation();
                                    handleToggleStatus(workflow);
                                  }}
                                >
                                  {workflow.status === 'active' ? (
                                    <PauseIcon className="w-4 h-4" />
                                  ) : (
                                    <PlayIcon className="w-4 h-4" />
                                  )}
                                </Button>
                                <Button
                                  className="p-1.5"
                                  color="zinc"
                                  onClick={(e: React.MouseEvent) => {
                                    e.stopPropagation();
                                    handleWorkflowEdit(workflow);
                                  }}
                                >
                                  <PencilIcon className="w-4 h-4" />
                                </Button>
                                <Button
                                  className="p-1.5"
                                  color="zinc"
                                  onClick={(e: React.MouseEvent) => {
                                    e.stopPropagation();
                                    handleWorkflowDuplicate(workflow);
                                  }}
                                >
                                  <DocumentDuplicateIcon className="w-4 h-4" />
                                </Button>
                                <Button
                                  className="p-1.5"
                                  color="red"
                                  onClick={(e: React.MouseEvent) => {
                                    e.stopPropagation();
                                    handleWorkflowDelete(workflow);
                                  }}
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Workflow Form Modal */}
      <WorkflowForm
        isOpen={formState.isOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        workflow={formState.workflow}
        mode={formState.mode}
      />
    </div>
  );
}
