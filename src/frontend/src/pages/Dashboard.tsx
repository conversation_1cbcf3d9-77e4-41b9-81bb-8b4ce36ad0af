import { Heading } from '../components/ui/catalyst/heading';
import { Text } from '../components/ui/catalyst/text';
import { Card, StatsCard, CardHeader, CardContent } from '../components/ui/card';
import {
  BoltIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

/**
 * Dashboard page - main landing page for authenticated users
 */
export default function Dashboard() {
  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header Section */}
      <div className="space-y-2">
        <Heading level={1}>Dashboard</Heading>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Active Workflows"
          value={12}
          icon={<BoltIcon />}
          color="primary"
          trend="up"
          trendValue="+12% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.1s' }}
        />

        <StatsCard
          title="Open Opportunities"
          value={24}
          icon={<ChartBarIcon />}
          color="success"
          trend="up"
          trendValue="+8% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.2s' }}
        />

        <StatsCard
          title="Pending Tasks"
          value={8}
          icon={<ClipboardDocumentListIcon />}
          color="warning"
          trend="down"
          trendValue="-4% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.3s' }}
        />

        <StatsCard
          title="Revenue This Month"
          value="$45.2K"
          icon={<CurrencyDollarIcon />}
          color="secondary"
          trend="up"
          trendValue="+15% from last month"
          className="animate-slide-up"
          style={{ animationDelay: '0.4s' }}
        />
      </div>

      {/* Recent Activity */}
      <Card
        className="animate-slide-up"
        style={{ animationDelay: '0.5s' }}
        shadow="medium"
      >
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-zinc-100 dark:bg-zinc-800">
              <ClockIcon className="h-5 w-5 text-zinc-600 dark:text-zinc-400" />
            </div>
            <Heading level={3}>Recent Activity</Heading>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900">
                <div className="h-2 w-2 rounded-full bg-primary-600 dark:bg-primary-400"></div>
              </div>
              <div className="flex-1 space-y-1">
                <Text className="!text-zinc-950 dark:!text-white font-medium">
                  New workflow "Lead Qualification" was created
                </Text>
                <Text className="!text-zinc-500 dark:!text-zinc-400 text-sm">
                  2 hours ago
                </Text>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-success-100 dark:bg-success-900">
                <div className="h-2 w-2 rounded-full bg-success-600 dark:bg-success-400"></div>
              </div>
              <div className="flex-1 space-y-1">
                <Text className="!text-zinc-950 dark:!text-white font-medium">
                  Opportunity "Enterprise Deal" moved to "Negotiation"
                </Text>
                <Text className="!text-zinc-500 dark:!text-zinc-400 text-sm">
                  4 hours ago
                </Text>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-warning-100 dark:bg-warning-900">
                <div className="h-2 w-2 rounded-full bg-warning-600 dark:bg-warning-400"></div>
              </div>
              <div className="flex-1 space-y-1">
                <Text className="!text-zinc-950 dark:!text-white font-medium">
                  Task "Follow up with client" was completed
                </Text>
                <Text className="!text-zinc-500 dark:!text-zinc-400 text-sm">
                  6 hours ago
                </Text>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
