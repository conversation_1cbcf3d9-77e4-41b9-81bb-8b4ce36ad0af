import React from 'react';
import { useParams } from 'react-router';
import { Heading } from '../components/ui/catalyst/heading';
import { Text } from '../components/ui/catalyst/text';

export default function OpportunityDetail() {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="space-y-6">
      <div>
        <Heading level={1}>Opportunity Details</Heading>
        <Text className="mt-2 text-gray-600">
          Opportunity ID: {id}
        </Text>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <Text className="text-gray-500">
          Opportunity detail interface will be implemented in Phase 1D
        </Text>
      </div>
    </div>
  );
}
