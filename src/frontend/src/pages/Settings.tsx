import React from 'react';
import { Heading } from '../components/ui/catalyst/heading';
import { Text } from '../components/ui/catalyst/text';

export default function Settings() {
  return (
    <div className="space-y-6">
      <div>
        <Heading level={1}>Settings</Heading>
        <Text className="mt-2 text-gray-600">
          Configure your application preferences
        </Text>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <Text className="text-gray-500">
          Settings interface will be implemented in future phases
        </Text>
      </div>
    </div>
  );
}
