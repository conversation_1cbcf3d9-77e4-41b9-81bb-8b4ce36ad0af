import React from 'react';
import { Heading } from '../components/ui/catalyst/heading';
import { Text } from '../components/ui/catalyst/text';
import { Button } from '../components/ui/catalyst/button';

export default function OpportunityList() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Heading level={1}>Opportunities</Heading>
          <Text className="mt-2 text-gray-600">
            Track and manage your sales opportunities
          </Text>
        </div>
        <Button color="green">
          Create Opportunity
        </Button>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <Text className="text-gray-500">
          Opportunity management interface will be implemented in Phase 1D
        </Text>
      </div>
    </div>
  );
}
