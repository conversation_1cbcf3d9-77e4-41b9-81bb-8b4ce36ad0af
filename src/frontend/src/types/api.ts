/**
 * API Response wrapper interface
 */
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  timestamp?: string;
}

/**
 * API Error interface
 */
export interface ApiError {
  message: string;
  code?: string | number;
  details?: any;
  timestamp?: string;
}

/**
 * Pagination interface
 */
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated response interface
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: Pagination;
}

/**
 * API request configuration
 */
export interface ApiRequestConfig {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  skipAuth?: boolean;
}

/**
 * HTTP methods
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * API endpoint configuration
 */
export interface ApiEndpoint {
  method: HttpMethod;
  url: string;
  requiresAuth?: boolean;
}

/**
 * Common entity fields
 */
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * User entity (extends the auth user)
 */
export interface User extends BaseEntity {
  email: string;
  name: string;
  role: string;
  isActive: boolean;
  lastLoginAt?: string;
}

/**
 * Workflow status types - simplified to active/inactive toggle
 */
export type WorkflowStatus = 'active' | 'inactive';

/**
 * Workflow entity
 */
export interface Workflow extends BaseEntity {
  name: string;
  description?: string;
  status: WorkflowStatus;
  isActive: boolean;
  version: number;
  configuration: Record<string, any>;
  ownerId: string;
  owner?: User;
  lastRunAt?: string;
  totalRuns?: number;
  successfulRuns?: number;
  failedRuns?: number;
  tags?: string[];
  // Sales metrics
  averageARR?: number; // Average Annual Recurring Revenue
  closedPercentage?: number; // Percentage of opportunities that closed
  totalOpportunities?: number; // Total opportunities processed (same as totalRuns)
}

/**
 * Opportunity entity
 */
export interface Opportunity extends BaseEntity {
  title: string;
  description?: string;
  status: 'open' | 'in_progress' | 'closed' | 'won' | 'lost';
  value?: number;
  currency?: string;
  assigneeId?: string;
  workflowId?: string;
  metadata?: Record<string, any>;
}

/**
 * Task entity
 */
export interface Task extends BaseEntity {
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigneeId?: string;
  opportunityId?: string;
  workflowId?: string;
  dueDate?: string;
  completedAt?: string;
}
