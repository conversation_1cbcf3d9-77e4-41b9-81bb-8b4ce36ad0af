import type { ApiError } from '../types/api';

/**
 * Check if an error is an API error
 */
export function isApiError(error: any): error is ApiError {
  return error && typeof error === 'object' && 'message' in error;
}

/**
 * Get user-friendly error message from API error
 */
export function getErrorMessage(error: unknown): string {
  if (isApiError(error)) {
    return error.message;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unexpected error occurred';
}

/**
 * Check if error is a network error
 */
export function isNetworkError(error: any): boolean {
  return isApiError(error) && error.code === 'NETWORK_ERROR';
}

/**
 * Check if error is an authentication error
 */
export function isAuthError(error: any): boolean {
  return isApiError(error) && (error.code === 401 || error.code === 403);
}

/**
 * Check if error is a validation error
 */
export function isValidationError(error: any): boolean {
  return isApiError(error) && error.code === 422;
}

/**
 * Check if error is a not found error
 */
export function isNotFoundError(error: any): boolean {
  return isApiError(error) && error.code === 404;
}

/**
 * Format API URL with parameters
 */
export function formatApiUrl(baseUrl: string, params?: Record<string, any>): string {
  if (!params) return baseUrl;
  
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `${baseUrl}?${queryString}` : baseUrl;
}

/**
 * Debounce function for search queries
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Create a delay for testing loading states
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Don't retry on client errors
      if (isApiError(error) && typeof error.code === 'number' && error.code >= 400 && error.code < 500) {
        throw error;
      }
      
      // Wait before retrying with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Transform data for API requests
 */
export function transformForApi<T>(data: T): T {
  // Remove undefined values and transform dates to ISO strings
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  const transformed = {} as T;
  
  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      if (value instanceof Date) {
        (transformed as any)[key] = value.toISOString();
      } else if (typeof value === 'object' && value !== null) {
        (transformed as any)[key] = transformForApi(value);
      } else {
        (transformed as any)[key] = value;
      }
    }
  });
  
  return transformed;
}

/**
 * Parse dates from API responses
 */
export function parseDatesFromApi<T>(data: T): T {
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  if (Array.isArray(data)) {
    return data.map(item => parseDatesFromApi(item)) as T;
  }
  
  const parsed = {} as T;
  
  Object.entries(data).forEach(([key, value]) => {
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
      // Parse ISO date strings
      (parsed as any)[key] = new Date(value);
    } else if (typeof value === 'object' && value !== null) {
      (parsed as any)[key] = parseDatesFromApi(value);
    } else {
      (parsed as any)[key] = value;
    }
  });
  
  return parsed;
}
