import { type FullConfig } from "@playwright/test";
import fs from "fs";

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Cleaning up authentication state...');

  // Clean up the auth file if it exists
  const authFile = "playwright/.auth/user.json";

  try {
    if (fs.existsSync(authFile)) {
      fs.unlinkSync(authFile);
      console.log('✅ Authentication state file cleaned up');
    }
  } catch (error) {
    console.warn('⚠️ Could not clean up auth file:', error);
  }

  console.log('✅ Teardown completed');
}

export default globalTeardown;