import { chromium, type FullConfig } from "@playwright/test";
import dotenv from "dotenv";

dotenv.config();

export const authFile = "playwright/.auth/user.json";

// Test user credentials for OTP flow
const TEST_USER_EMAIL = "<EMAIL>";
const TEST_USER_OTP = "123456";

async function globalSetup(config: FullConfig) {
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    console.log('🔐 Setting up authentication with OTP flow...');
    console.log('📧 Test user email:', TEST_USER_EMAIL);

    const baseURL = config.projects[0].use.baseURL || "http://localhost:4174";
    console.log('🌐 Base URL:', baseURL);

    // Navigate to the application
    await page.goto(baseURL);
    await page.waitForLoadState('networkidle');

    console.log('⏳ Waiting for Descope component to load...');

    // Wait for Descope component to load
    const descopeComponent = page.locator('descope-wc');
    await descopeComponent.waitFor({ state: 'visible', timeout: 15000 });

    // Wait for the component to fully initialize
    await page.waitForTimeout(5000);

    console.log('✅ Descope component loaded');

    // Look for email input field
    console.log('🔍 Looking for email input field...');

    // Try different selectors for the email input
    const emailSelectors = [
      'input[type="email"]',
      'input[name="email"]',
      'input[placeholder*="email" i]',
      'descope-wc input[type="email"]',
      'descope-wc input[name="email"]',
      '[data-id="email"]',
      '[data-testid="email"]'
    ];

    let emailInput = null;
    for (const selector of emailSelectors) {
      const input = page.locator(selector);
      if (await input.isVisible().catch(() => false)) {
        emailInput = input;
        console.log(`✅ Found email input with selector: ${selector}`);
        break;
      }
    }

    if (!emailInput) {
      // Take screenshot for debugging
      await page.screenshot({ path: 'test-results/auth-setup-no-email-input.png', fullPage: true });
      throw new Error('Could not find email input field');
    }

    // Fill in the email
    console.log('📝 Filling email field...');
    await emailInput.fill(TEST_USER_EMAIL);

    // Look for continue/submit button
    console.log('🔍 Looking for submit button...');

    // Wait a moment for the form to update after filling email
    await page.waitForTimeout(2000);

    const submitSelectors = [
      'button[type="submit"]',
      'button:has-text("Continue")',
      'button:has-text("Send")',
      'button:has-text("Next")',
      'button:has-text("Submit")',
      'button:has-text("Sign")',
      'button:has-text("Login")',
      'button:has-text("Verify")',
      '[data-id="continue"]',
      '[data-testid="continue"]',
      'descope-wc button',
      'button',
      '[role="button"]'
    ];

    let submitButton = null;
    for (const selector of submitSelectors) {
      const buttons = page.locator(selector);
      const count = await buttons.count();

      for (let i = 0; i < count; i++) {
        const button = buttons.nth(i);
        const isVisible = await button.isVisible().catch(() => false);
        const isEnabled = await button.isEnabled().catch(() => false);

        if (isVisible && isEnabled) {
          const text = await button.textContent().catch(() => '');
          console.log(`🔍 Found button: "${text?.trim()}" with selector: ${selector}`);

          // Skip buttons that are clearly not submit buttons
          if (text && (
            text.toLowerCase().includes('google') ||
            text.toLowerCase().includes('apple') ||
            text.toLowerCase().includes('facebook') ||
            text.toLowerCase().includes('github') ||
            text.toLowerCase().includes('microsoft')
          )) {
            continue;
          }

          submitButton = button;
          console.log(`✅ Selected submit button: "${text?.trim()}" with selector: ${selector}`);
          break;
        }
      }

      if (submitButton) break;
    }

    if (!submitButton) {
      await page.screenshot({ path: 'test-results/auth-setup-no-submit-button.png', fullPage: true });

      // Log all buttons for debugging
      const allButtons = page.locator('button, [role="button"]');
      const buttonCount = await allButtons.count();
      console.log(`🔍 Debug: Found ${buttonCount} total buttons:`);

      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        const button = allButtons.nth(i);
        const text = await button.textContent().catch(() => '');
        const isVisible = await button.isVisible().catch(() => false);
        const isEnabled = await button.isEnabled().catch(() => false);
        console.log(`  ${i + 1}. "${text?.trim()}" (visible: ${isVisible}, enabled: ${isEnabled})`);
      }

      throw new Error('Could not find submit button');
    }

    // Click submit to proceed to OTP step
    console.log('🖱️ Clicking submit button...');
    await submitButton.click();

    // Wait for OTP input to appear
    console.log('⏳ Waiting for OTP input field...');
    await page.waitForTimeout(5000);

    // Take screenshot to see current state
    await page.screenshot({ path: 'test-results/auth-setup-after-email-submit.png', fullPage: true });

    // Look for OTP input fields - Descope often uses multiple tel inputs for OTP
    const otpSelectors = [
      'input[type="tel"]',
      'input[type="text"]',
      'input[type="number"]',
      'input[name="otp"]',
      'input[name="code"]',
      'input[placeholder*="code" i]',
      'input[placeholder*="otp" i]',
      'input[placeholder*="verification" i]',
      'input[placeholder*="6" i]',
      '[data-id="otp"]',
      '[data-testid="otp"]',
      '[data-id="code"]',
      '[data-testid="code"]',
      'descope-wc input[type="tel"]',
      'descope-wc input[type="text"]',
      'descope-wc input[type="number"]'
    ];

    let otpInputs = null;
    console.log('🔍 Looking for OTP input field...');

    for (const selector of otpSelectors) {
      const inputs = page.locator(selector);
      const count = await inputs.count();

      if (count > 0) {
        const firstInput = inputs.first();
        const isVisible = await firstInput.isVisible().catch(() => false);

        if (isVisible) {
          const placeholder = await firstInput.getAttribute('placeholder').catch(() => '');
          const name = await firstInput.getAttribute('name').catch(() => '');
          const type = await firstInput.getAttribute('type').catch(() => '');

          console.log(`🔍 Found ${count} inputs: type="${type}", name="${name}", placeholder="${placeholder}" with selector: ${selector}`);

          // If we found tel inputs (common for OTP), use them
          if (type === 'tel' || count >= 4) {
            otpInputs = inputs;
            console.log(`✅ Selected OTP inputs: ${count} fields of type="${type}"`);
            break;
          }

          // Check if this looks like an OTP field
          if (
            placeholder?.toLowerCase().includes('code') ||
            placeholder?.toLowerCase().includes('otp') ||
            placeholder?.toLowerCase().includes('verification') ||
            name?.toLowerCase().includes('otp') ||
            name?.toLowerCase().includes('code') ||
            placeholder?.includes('6') ||
            (type === 'text' && !name?.toLowerCase().includes('email'))
          ) {
            otpInputs = inputs;
            console.log(`✅ Selected OTP input: type="${type}", name="${name}", placeholder="${placeholder}"`);
            break;
          }
        }
      }
    }

    if (!otpInputs) {
      await page.screenshot({ path: 'test-results/auth-setup-no-otp-input.png', fullPage: true });

      // Log all inputs for debugging
      const allInputs = page.locator('input');
      const inputCount = await allInputs.count();
      console.log(`🔍 Debug: Found ${inputCount} total inputs:`);

      for (let i = 0; i < Math.min(inputCount, 10); i++) {
        const input = allInputs.nth(i);
        const type = await input.getAttribute('type').catch(() => '');
        const name = await input.getAttribute('name').catch(() => '');
        const placeholder = await input.getAttribute('placeholder').catch(() => '');
        const isVisible = await input.isVisible().catch(() => false);
        console.log(`  ${i + 1}. type="${type}", name="${name}", placeholder="${placeholder}" (visible: ${isVisible})`);
      }

      throw new Error('Could not find OTP input field');
    }

    // Fill in the OTP
    console.log('🔢 Filling OTP field...');
    const inputCount = await otpInputs.count();

    if (inputCount === 1) {
      // Single input field - fill with complete OTP
      await otpInputs.first().fill(TEST_USER_OTP);
    } else if (inputCount >= 6) {
      // Multiple input fields (one per digit) - fill each digit
      const digits = TEST_USER_OTP.split('');
      for (let i = 0; i < Math.min(digits.length, inputCount); i++) {
        await otpInputs.nth(i).fill(digits[i]);
        await page.waitForTimeout(100); // Small delay between digits
      }
    } else {
      // Try filling the first input with the complete OTP
      await otpInputs.first().fill(TEST_USER_OTP);
    }

    // Look for OTP submit button
    console.log('🔍 Looking for OTP submit button...');

    // Wait a moment for the form to update after filling OTP
    await page.waitForTimeout(2000);

    let otpSubmitButton = null;
    for (const selector of submitSelectors) {
      const buttons = page.locator(selector);
      const count = await buttons.count();

      for (let i = 0; i < count; i++) {
        const button = buttons.nth(i);
        const isVisible = await button.isVisible().catch(() => false);
        const isEnabled = await button.isEnabled().catch(() => false);

        if (isVisible && isEnabled) {
          const text = await button.textContent().catch(() => '');
          console.log(`🔍 Found button: "${text?.trim()}" with selector: ${selector}`);

          // Skip social login buttons and logout buttons
          if (text && (
            text.toLowerCase().includes('google') ||
            text.toLowerCase().includes('apple') ||
            text.toLowerCase().includes('facebook') ||
            text.toLowerCase().includes('github') ||
            text.toLowerCase().includes('sign out') ||
            text.toLowerCase().includes('logout') ||
            text.toLowerCase().includes('log out')
          )) {
            continue;
          }

          otpSubmitButton = button;
          console.log(`✅ Selected OTP submit button: "${text?.trim()}" with selector: ${selector}`);
          break;
        }
      }

      if (otpSubmitButton) break;
    }

    if (!otpSubmitButton) {
      await page.screenshot({ path: 'test-results/auth-setup-no-otp-submit.png', fullPage: true });

      // Log all buttons for debugging
      const allButtons = page.locator('button, [role="button"]');
      const buttonCount = await allButtons.count();
      console.log(`🔍 Debug: Found ${buttonCount} total buttons after OTP fill:`);

      let hasSignOutButton = false;
      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        const button = allButtons.nth(i);
        const text = await button.textContent().catch(() => '');
        const isVisible = await button.isVisible().catch(() => false);
        const isEnabled = await button.isEnabled().catch(() => false);
        console.log(`  ${i + 1}. "${text?.trim()}" (visible: ${isVisible}, enabled: ${isEnabled})`);

        if (text && text.toLowerCase().includes('sign out')) {
          hasSignOutButton = true;
        }
      }

      // If we see a Sign Out button, authentication might have completed automatically
      if (hasSignOutButton) {
        console.log('🎉 Found Sign Out button - authentication may have completed automatically!');
        // Skip the submit step and go directly to verification
      } else {
        throw new Error('Could not find OTP submit button');
      }
    } else {
      // Submit OTP
      console.log('🖱️ Submitting OTP...');
      await otpSubmitButton.click();
    }

    // Wait for authentication to complete
    console.log('⏳ Waiting for authentication to complete...');
    await page.waitForTimeout(10000);

    // Take screenshot to see current state
    await page.screenshot({ path: 'test-results/auth-setup-after-otp-submit.png', fullPage: true });

    // Check if we're authenticated by looking for various success indicators
    const authSuccessSelectors = [
      'text=You are successfully authenticated with Shevet',
      'text=Welcome, User!',
      'text=Welcome,',
      'button:has-text("Sign Out")',
      'text=User Info:',
      'text=Email: <EMAIL>'
    ];

    let authSuccess = false;
    for (const selector of authSuccessSelectors) {
      const isVisible = await page.locator(selector).isVisible().catch(() => false);
      if (isVisible) {
        console.log(`✅ Found authentication success indicator: ${selector}`);
        authSuccess = true;
        break;
      }
    }

    // Also check if we're no longer on the login form
    const loginFormVisible = await page.locator('text=Please sign in to continue').isVisible().catch(() => false);
    const descopeVisible = await page.locator('descope-wc').isVisible().catch(() => false);

    if (!loginFormVisible && !descopeVisible) {
      console.log('✅ Login form is no longer visible - likely authenticated');
      authSuccess = true;
    }

    if (!authSuccess) {
      await page.screenshot({ path: 'test-results/auth-setup-failed.png', fullPage: true });

      // Log current page content for debugging
      const pageContent = await page.textContent('body').catch(() => '');
      console.log('🔍 Current page content:', (pageContent || '').substring(0, 500));

      throw new Error('Authentication did not complete successfully');
    }

    console.log('🎉 Authentication successful!');

    // Save the authentication state
    await page.context().storageState({ path: authFile });
    console.log('💾 Authentication state saved to:', authFile);

    // Take success screenshot
    await page.screenshot({ path: 'test-results/auth-setup-success.png', fullPage: true });

  } catch (error) {
    console.error('❌ Auth setup failed:', error);
    await page.screenshot({ path: 'test-results/auth-setup-error.png', fullPage: true });
    throw error; // Re-throw to fail the setup if authentication fails
  } finally {
    await browser.close();
  }
}

export default globalSetup;