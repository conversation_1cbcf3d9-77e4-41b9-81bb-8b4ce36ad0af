import { useAuth } from './hooks/useAuth';
import { Descope } from '@descope/react-sdk';
import { LoadingSpinner } from './components/ui/LoadingSpinner';
import { Outlet } from 'react-router';

/**
 * Main App component that handles authentication state
 * Shows Descope login for unauthenticated users, loading spinner while checking auth,
 * and the main application layout for authenticated users
 */
const App = () => {
  const { isAuthenticated, isLoading } = useAuth();

  console.log('Auth values:', { isAuthenticated, isLoading });

  // Show loading spinner while checking authentication
  if (isLoading) {
    console.log('Showing loading state');
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="xl" />
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show Descope login if not authenticated
  if (!isAuthenticated) {
    console.log('Showing login state');
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-indigo-100">
              <span className="text-xl font-bold text-indigo-600">IL</span>
            </div>
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              Welcome to Shevet
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Please sign in to continue
            </p>
          </div>
          <div className="mt-8">
            <Descope
              flowId="sign-in-otp-or-social"
              onSuccess={(e) => {
                console.log('Login successful:', e.detail.user);
              }}
              onError={(e) => {
                console.error('Login error:', e);
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated - render the main application
  // The routing and layout will be handled by React Router and our Layout component
  return <Outlet />;
};

export default App;