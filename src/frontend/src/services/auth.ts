import { getSessionToken } from '@descope/react-sdk';

/**
 * Authentication service that works with Descope SDK
 * Provides utilities for token management and authentication state
 */
export class AuthService {
  /**
   * Get the current session token from Descope
   * @returns The session token or null if not authenticated
   */
  static getToken(): string | null {
    try {
      return getSessionToken();
    } catch (error) {
      console.error('Error getting session token:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated by verifying token exists
   * @returns True if user has a valid session token
   */
  static isAuthenticated(): boolean {
    const token = this.getToken();
    return token !== null && token !== undefined && token.length > 0;
  }

  /**
   * Get authorization header for API requests
   * @returns Authorization header object or empty object if not authenticated
   */
  static getAuthHeader(): Record<string, string> {
    const token = this.getToken();
    if (!token) {
      return {};
    }
    
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  /**
   * Get common headers for API requests including auth and content type
   * @returns Headers object with authorization and content type
   */
  static getApiHeaders(): Record<string, string> {
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      ...this.getAuthHeader(),
    };
  }
}

/**
 * User role types for role-based access control
 */
export type UserRole = 'admin' | 'user' | 'viewer';

/**
 * Extended user interface that includes role information
 */
export interface AppUser {
  id: string;
  name: string;
  email: string;
  role?: UserRole;
  permissions?: string[];
}

/**
 * Authentication state interface
 */
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: AppUser | null;
  token: string | null;
}
