import { useNavigate, useLocation } from 'react-router';
import { Navbar, NavbarItem, NavbarSection, NavbarSpacer } from '../ui/catalyst/navbar';
import { Dropdown, DropdownButton, DropdownItem, DropdownMenu } from '../ui/catalyst/dropdown';
import { Avatar } from '../ui/catalyst/avatar';
import { useAuth } from '../../hooks/useAuth';
import { ROUTES } from '../../routes/routes';
import {
  UserIcon,
  ArrowRightStartOnRectangleIcon,
  BellIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

/**
 * Header component with user menu and navigation
 * Uses Catalyst Navbar for consistent styling
 */
export function Header() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleProfileClick = () => {
    navigate(ROUTES.PROFILE);
  };

  const handleSettingsClick = () => {
    navigate(ROUTES.SETTINGS);
  };

  const handleLogout = () => {
    logout();
  };

  // Get page title based on current route
  const getPageTitle = () => {
    switch (location.pathname) {
      case ROUTES.DASHBOARD:
        return 'Dashboard';
      case ROUTES.WORKFLOWS:
        return 'Workflows';
      case ROUTES.OPPORTUNITIES:
        return 'Opportunities';
      case ROUTES.SETTINGS:
        return 'Settings';
      default:
        return 'Shevet';
    }
  };

  return (
    <Navbar className="border-b border-zinc-200 bg-white/80 backdrop-blur-sm dark:border-zinc-700 dark:bg-zinc-900/80">
      <NavbarSection>
        {/* Breadcrumb / Page Title */}
        <div className="flex items-center space-x-2">
          <h1 className="text-lg font-semibold text-zinc-900 dark:text-white">
            {getPageTitle()}
          </h1>
        </div>
      </NavbarSection>

      <NavbarSpacer />

      <NavbarSection className="flex items-center space-x-4">
        {/* Notifications */}
        <NavbarItem className="relative">
          <button className="relative rounded-lg p-2 text-zinc-500 hover:bg-zinc-100 hover:text-zinc-700 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-300 transition-colors">
            <BellIcon className="h-5 w-5" />
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-primary-500 text-xs font-medium text-white flex items-center justify-center">
              3
            </span>
          </button>
        </NavbarItem>

        {/* User Menu */}
        <Dropdown>
          <DropdownButton as={NavbarItem} className="flex items-center space-x-3 rounded-lg p-2 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors">
            <Avatar
              src={undefined}
              initials={user?.name?.split(' ').map(n => n[0]).join('') || 'U'}
              className="h-8 w-8 ring-2 ring-white dark:ring-zinc-800"
            />
            <div className="hidden sm:block text-left">
              <p className="text-sm font-medium text-zinc-900 dark:text-white">
                {user?.name || 'User'}
              </p>
              <p className="text-xs text-zinc-500 dark:text-zinc-400">
                {user?.email || '<EMAIL>'}
              </p>
            </div>
          </DropdownButton>

          <DropdownMenu className="min-w-64" anchor="bottom end">
            <div className="px-4 py-3 border-b border-zinc-200 dark:border-zinc-700">
              <p className="text-sm font-medium text-zinc-900 dark:text-white">
                {user?.name || 'User'}
              </p>
              <p className="text-xs text-zinc-500 dark:text-zinc-400">
                {user?.email || '<EMAIL>'}
              </p>
            </div>

            <DropdownItem onClick={handleProfileClick}>
              <div className="flex items-center space-x-3">
                <UserIcon className="h-4 w-4" />
                <span>Profile</span>
              </div>
            </DropdownItem>

            <DropdownItem onClick={handleSettingsClick}>
              <div className="flex items-center space-x-3">
                <Cog6ToothIcon className="h-4 w-4" />
                <span>Settings</span>
              </div>
            </DropdownItem>

            <div className="border-t border-zinc-200 dark:border-zinc-700 my-1" />

            <DropdownItem onClick={handleLogout}>
              <div className="flex items-center space-x-3 text-error-600 dark:text-error-400">
                <ArrowRightStartOnRectangleIcon className="h-4 w-4" />
                <span>Sign Out</span>
              </div>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </NavbarSection>
    </Navbar>
  );
}
