import { Suspense } from 'react';
import { Outlet } from 'react-router';
import { SidebarLayout } from '../ui/catalyst/sidebar-layout';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { LoadingSpinner } from '../ui/LoadingSpinner';

/**
 * Main layout component that provides the application structure
 * Uses Catalyst SidebarLayout for responsive sidebar behavior
 */
export function Layout() {
  return (
    <SidebarLayout
      sidebar={<Sidebar />}
      navbar={<Header />}
    >
      <main className="flex-1 overflow-auto">
        <div className="p-6">
          <Suspense 
            fallback={
              <div className="flex items-center justify-center h-64">
                <LoadingSpinner size="lg" />
              </div>
            }
          >
            <Outlet />
          </Suspense>
        </div>
      </main>
    </SidebarLayout>
  );
}
