import { useLocation } from 'react-router';
import {
  Sidebar as CatalystSidebar,
  SidebarBody,
  SidebarFooter,
  SidebarHeader,
  SidebarItem,
  SidebarLabel,
  SidebarSection,
  SidebarSpacer
} from '../ui/catalyst/sidebar';
import { ROUTES } from '../../routes/routes';
import {
  HomeIcon,
  BoltIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  ArrowRightStartOnRectangleIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../hooks/useAuth';
import { Avatar } from '../ui/catalyst/avatar';

/**
 * Navigation items configuration with modern icons
 */
const navigationItems = [
  {
    name: 'Dashboard',
    href: ROUTES.DASHBOARD,
    icon: <HomeIcon className="w-5 h-5" />,
    description: 'Overview and analytics',
  },
  {
    name: 'Workflows',
    href: ROUTES.WORKFLOWS,
    icon: <BoltIcon className="w-5 h-5" />,
    description: 'Automated processes',
  },
  {
    name: 'Opportunities',
    href: ROUTES.OPPORTUNITIES,
    icon: <ChartBarIcon className="w-5 h-5" />,
    description: 'Sales pipeline',
  },
];

/**
 * Sidebar navigation component
 * Uses Catalyst Sidebar components for consistent styling and behavior
 */
export function Sidebar() {
  const { user, logout } = useAuth();
  const location = useLocation();

  const isCurrentPath = (href: string) => {
    if (href === ROUTES.DASHBOARD) {
      return location.pathname === href;
    }
    return location.pathname.startsWith(href);
  };

  return (
    <CatalystSidebar>
      <SidebarHeader>
        <div className="flex items-center gap-3">
          <div className="flex flex-col">
            <SidebarLabel className="text-lg font-bold tracking-tight text-zinc-800 dark:text-zinc-100">Shevet</SidebarLabel>
            <span className="text-xs text-zinc-500 dark:text-zinc-400">Workflow Platform</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarBody>
        {/* Main Navigation */}
        <SidebarSection>
          <div className="space-y-1">
            {navigationItems.map((item) => (
              <SidebarItem
                key={item.name}
                href={item.href}
                current={isCurrentPath(item.href)}
                className="group relative"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <SidebarLabel className="font-medium">{item.name}</SidebarLabel>
                    <p className="text-xs text-zinc-500 dark:text-zinc-400 group-hover:text-zinc-600 dark:group-hover:text-zinc-300 transition-colors">
                      {item.description}
                    </p>
                  </div>
                </div>
              </SidebarItem>
            ))}
          </div>
        </SidebarSection>
        <SidebarItem
          href={ROUTES.SETTINGS}
          current={isCurrentPath(ROUTES.SETTINGS)}
          className="group"
        >
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <Cog6ToothIcon className="w-5 h-5" />
            </div>
            <div className="flex-1 min-w-0">
              <SidebarLabel className="font-medium">Settings</SidebarLabel>
              <p className="text-xs text-zinc-500 dark:text-zinc-400 group-hover:text-zinc-600 dark:group-hover:text-zinc-300 transition-colors">
                Preferences & config
              </p>
            </div>
          </div>
        </SidebarItem>
        <SidebarSpacer />
        <SidebarSection>
          {/* {user?.role?.includes("Tenant Admin") ? (
            <SidebarItem href={ROUTES.ADMIN}>
              <Cog6ToothIcon />
              <SidebarLabel>Admin</SidebarLabel>
            </SidebarItem>
          ) : null} */}
          <SidebarItem href="/support">
            <QuestionMarkCircleIcon />
            <SidebarLabel>Support</SidebarLabel>
          </SidebarItem>
          <SidebarItem onClick={logout}>
            <ArrowRightStartOnRectangleIcon />
            <SidebarLabel>Sign out</SidebarLabel>
          </SidebarItem>
        </SidebarSection>
      </SidebarBody>
      <SidebarFooter>
        <SidebarSection>
          <SidebarItem href={ROUTES.PROFILE}>
            <Avatar src="/profile-photo.jpg" />
            <SidebarLabel>{user?.name}</SidebarLabel>
            <ChevronRightIcon />
          </SidebarItem>
        </SidebarSection>
      </SidebarFooter>
    </CatalystSidebar>
  );
}
