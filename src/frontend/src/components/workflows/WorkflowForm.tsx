import React, { useState, useEffect } from 'react';
import { Workflow, WorkflowStatus } from '../../types/api';
import { Dialog, DialogTitle, DialogBody, DialogActions } from '../ui/catalyst/dialog';
import { Button } from '../ui/catalyst/button';
import { Input } from '../ui/catalyst/input';
import { Textarea } from '../ui/catalyst/textarea';
import { Listbox, ListboxOption } from '../ui/catalyst/listbox';
import { Field, FieldGroup, Label } from '../ui/catalyst/fieldset';
import { Text } from '../ui/catalyst/text';
import { Badge } from '../ui/catalyst/badge';
import { XMarkIcon, PlusIcon } from '@heroicons/react/24/outline';

interface WorkflowFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (workflow: Partial<Workflow>) => Promise<void>;
  workflow?: Workflow | null;
  mode: 'create' | 'edit' | 'duplicate';
}

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

export function WorkflowForm({ isOpen, onClose, onSubmit, workflow, mode }: WorkflowFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'inactive' as WorkflowStatus,
    tags: [] as string[],
  });
  const [newTag, setNewTag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when workflow or mode changes
  useEffect(() => {
    if (workflow && (mode === 'edit' || mode === 'duplicate')) {
      setFormData({
        name: mode === 'duplicate' ? `${workflow.name} (Copy)` : workflow.name,
        description: workflow.description || '',
        status: mode === 'duplicate' ? 'inactive' : workflow.status,
        tags: workflow.tags || [],
      });
    } else {
      setFormData({
        name: '',
        description: '',
        status: 'inactive',
        tags: [],
      });
    }
    setErrors({});
  }, [workflow, mode, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Workflow name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Workflow name must be at least 3 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const workflowData: Partial<Workflow> = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        status: formData.status,
        tags: formData.tags,
        isActive: formData.status === 'active',
      };

      if (mode === 'edit' && workflow) {
        workflowData.id = workflow.id;
        workflowData.version = workflow.version;
      } else if (mode === 'duplicate' && workflow) {
        workflowData.configuration = workflow.configuration;
      }

      await onSubmit(workflowData);
      onClose();
    } catch (error) {
      console.error('Failed to save workflow:', error);
      setErrors({ submit: 'Failed to save workflow. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddTag = () => {
    const tag = newTag.trim().toLowerCase();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag],
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'create':
        return 'Create New Workflow';
      case 'edit':
        return 'Edit Workflow';
      case 'duplicate':
        return 'Duplicate Workflow';
      default:
        return 'Workflow';
    }
  };

  const getSubmitText = () => {
    if (isSubmitting) {
      return mode === 'create' ? 'Creating...' : mode === 'edit' ? 'Saving...' : 'Duplicating...';
    }
    return mode === 'create' ? 'Create Workflow' : mode === 'edit' ? 'Save Changes' : 'Duplicate Workflow';
  };

  return (
    <Dialog open={isOpen} onClose={onClose} size="2xl">
      <form onSubmit={handleSubmit}>
        <DialogTitle>{getTitle()}</DialogTitle>
        
        <DialogBody className="space-y-6">
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <Text className="text-red-700">{errors.submit}</Text>
            </div>
          )}

          <FieldGroup>
            <Field>
              <Label>Workflow Name *</Label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter workflow name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <Text className="text-red-600 text-sm mt-1">{errors.name}</Text>
              )}
            </Field>

            <Field>
              <Label>Description</Label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this workflow does"
                rows={3}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <Text className="text-red-600 text-sm mt-1">{errors.description}</Text>
              )}
              <Text className="text-zinc-500 text-sm mt-1">
                {formData.description.length}/500 characters
              </Text>
            </Field>

            <Field>
              <Label>Status</Label>
              <Listbox
                value={formData.status}
                onChange={(value) => setFormData(prev => ({ ...prev, status: value as WorkflowStatus }))}
              >
                {statusOptions.map((option) => (
                  <ListboxOption key={option.value} value={option.value}>
                    {option.label}
                  </ListboxOption>
                ))}
              </Listbox>
            </Field>

            <Field>
              <Label>Tags</Label>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Add a tag"
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={handleAddTag}
                    color="zinc"
                    disabled={!newTag.trim()}
                  >
                    <PlusIcon className="w-4 h-4" />
                  </Button>
                </div>
                
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag) => (
                      <Badge
                        key={tag}
                        color="zinc"
                        className="flex items-center gap-1 pr-1"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 hover:bg-zinc-200 dark:hover:bg-zinc-700 rounded-full p-0.5"
                        >
                          <XMarkIcon className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </Field>
          </FieldGroup>
        </DialogBody>

        <DialogActions>
          <Button type="button" onClick={onClose} color="zinc">
            Cancel
          </Button>
          <Button
            type="submit"
            color="green"
            disabled={isSubmitting}
          >
            {getSubmitText()}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}
