import React from 'react';
import { Workflow } from '../../types/api';
import { Card } from '../ui/card';
import { Button } from '../ui/catalyst/button';
import { Badge } from '../ui/catalyst/badge';
import { Text } from '../ui/catalyst/text';
import { Heading } from '../ui/catalyst/heading';
import {
  PlayIcon,
  PauseIcon,
  DocumentDuplicateIcon,
  PencilIcon,
  TrashIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import clsx from 'clsx';

interface WorkflowCardProps {
  workflow: Workflow;
  onEdit?: (workflow: Workflow) => void;
  onDuplicate?: (workflow: Workflow) => void;
  onDelete?: (workflow: Workflow) => void;
  onToggleStatus?: (workflow: Workflow) => void;
  onView?: (workflow: Workflow) => void;
}

const statusConfig = {
  active: {
    color: 'green' as const,
    label: 'Active',
  },
  inactive: {
    color: 'zinc' as const,
    label: 'Inactive',
  },
};

export function WorkflowCard({
  workflow,
  onEdit,
  onDuplicate,
  onDelete,
  onToggleStatus,
  onView,
}: WorkflowCardProps) {
  const statusInfo = statusConfig[workflow.status];

  const formatCurrency = (amount: number) => {
    if (amount === 0) return '$0';
    if (amount >= 1000000) return `$${(amount / 1000000).toFixed(1)}M`;
    if (amount >= 1000) return `$${(amount / 1000).toFixed(0)}K`;
    return `$${amount.toLocaleString()}`;
  };

  const handleCardClick = () => {
    onView?.(workflow);
  };

  const handleToggleStatus = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleStatus?.(workflow);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(workflow);
  };

  const handleDuplicate = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDuplicate?.(workflow);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(workflow);
  };

  return (
    <Card
      className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
      padding="lg"
      shadow="medium"
      hover
      onClick={handleCardClick}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3">
              <Heading level={3} className="truncate">
                {workflow.name}
              </Heading>
              <Badge color={statusInfo.color}>
                {statusInfo.label}
              </Badge>
            </div>
            <Text className="mt-1 text-sm text-zinc-500 dark:text-zinc-400">
              v{workflow.version} • Created by {workflow.owner?.name}
            </Text>
          </div>

          {/* Actions Menu */}
          <Menu as="div" className="relative">
            <MenuButton
              className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              <EllipsisVerticalIcon className="w-5 h-5 text-zinc-500" />
            </MenuButton>
            <MenuItems className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-zinc-800 dark:ring-white dark:ring-opacity-10">
              <div className="py-1">
                <MenuItem>
                  {({ active }) => (
                    <button
                      onClick={handleEdit}
                      className={clsx(
                        'flex w-full items-center px-4 py-2 text-sm',
                        active
                          ? 'bg-zinc-100 text-zinc-900 dark:bg-zinc-700 dark:text-white'
                          : 'text-zinc-700 dark:text-zinc-300'
                      )}
                    >
                      <PencilIcon className="mr-3 h-4 w-4" />
                      Edit
                    </button>
                  )}
                </MenuItem>
                <MenuItem>
                  {({ active }) => (
                    <button
                      onClick={handleDuplicate}
                      className={clsx(
                        'flex w-full items-center px-4 py-2 text-sm',
                        active
                          ? 'bg-zinc-100 text-zinc-900 dark:bg-zinc-700 dark:text-white'
                          : 'text-zinc-700 dark:text-zinc-300'
                      )}
                    >
                      <DocumentDuplicateIcon className="mr-3 h-4 w-4" />
                      Duplicate
                    </button>
                  )}
                </MenuItem>
                <MenuItem>
                  {({ active }) => (
                    <button
                      onClick={handleDelete}
                      className={clsx(
                        'flex w-full items-center px-4 py-2 text-sm',
                        active
                          ? 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400'
                          : 'text-red-600 dark:text-red-400'
                      )}
                    >
                      <TrashIcon className="mr-3 h-4 w-4" />
                      Delete
                    </button>
                  )}
                </MenuItem>
              </div>
            </MenuItems>
          </Menu>
        </div>

        {/* Description */}
        {workflow.description && (
          <Text className="text-sm text-zinc-600 dark:text-zinc-400 line-clamp-2">
            {workflow.description}
          </Text>
        )}

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 pt-4 border-t border-zinc-200 dark:border-zinc-700">
          <div className="text-center">
            <Text className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {workflow.totalOpportunities || 0}
            </Text>
            <Text className="text-xs text-zinc-500 dark:text-zinc-400">
              Opportunities
            </Text>
          </div>
          <div className="text-center">
            <Text className="text-lg font-semibold text-green-600 dark:text-green-400">
              {formatCurrency(workflow.averageARR || 0)}
            </Text>
            <Text className="text-xs text-zinc-500 dark:text-zinc-400">
              Average ARR
            </Text>
          </div>
          <div className="text-center">
            <Text className={`text-lg font-semibold ${
              (workflow.closedPercentage || 0) >= 60
                ? 'text-green-600 dark:text-green-400'
                : (workflow.closedPercentage || 0) >= 40
                  ? 'text-yellow-600 dark:text-yellow-400'
                  : 'text-red-600 dark:text-red-400'
            }`}>
              {workflow.closedPercentage || 0}%
            </Text>
            <Text className="text-xs text-zinc-500 dark:text-zinc-400">
              Closed %
            </Text>
          </div>
        </div>

        {/* Tags */}
        {workflow.tags && workflow.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {workflow.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} color="zinc" className="text-xs">
                {tag}
              </Badge>
            ))}
            {workflow.tags.length > 3 && (
              <Badge color="zinc" className="text-xs">
                +{workflow.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-zinc-200 dark:border-zinc-700">
          <Button
            color={workflow.status === 'active' ? 'red' : 'green'}
            onClick={handleToggleStatus}
            className="flex items-center space-x-2 px-3 py-1.5 text-sm"
          >
            {workflow.status === 'active' ? (
              <>
                <PauseIcon className="w-4 h-4" />
                <span>Deactivate</span>
              </>
            ) : (
              <>
                <PlayIcon className="w-4 h-4" />
                <span>Activate</span>
              </>
            )}
          </Button>

          <Text className="text-xs text-zinc-500 dark:text-zinc-400">
            Updated {new Date(workflow.updatedAt).toLocaleDateString()}
          </Text>
        </div>
      </div>
    </Card>
  );
}
