import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from './button';
import { Alert } from './alert';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

/**
 * Error boundary component that catches JavaScript errors anywhere in the child component tree
 * Displays a fallback UI and optionally calls an error reporting function
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Update state with error info
    this.setState({ errorInfo });
    
    // Call optional error reporting function
    this.props.onError?.(error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <Alert color="red">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-red-800">
                    Something went wrong
                  </h3>
                  <p className="mt-2 text-red-700">
                    An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
                  </p>
                </div>

                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <div className="mt-4">
                    <details className="text-sm">
                      <summary className="cursor-pointer font-medium text-red-800">
                        Error Details (Development)
                      </summary>
                      <div className="mt-2 p-3 bg-red-50 rounded border border-red-200">
                        <p className="font-mono text-xs text-red-900 whitespace-pre-wrap">
                          {this.state.error.toString()}
                        </p>
                        {this.state.errorInfo && (
                          <p className="mt-2 font-mono text-xs text-red-800 whitespace-pre-wrap">
                            {this.state.errorInfo.componentStack}
                          </p>
                        )}
                      </div>
                    </details>
                  </div>
                )}

                <div className="flex space-x-3">
                  <Button 
                    color="red" 
                    onClick={this.handleReset}
                  >
                    Try Again
                  </Button>
                  <Button 
                    outline 
                    onClick={() => window.location.reload()}
                  >
                    Refresh Page
                  </Button>
                </div>
              </div>
            </Alert>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based error boundary for functional components
 */
interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
}

export function ErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="min-h-64 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Alert color="red">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-red-800">
                Error
              </h3>
              <p className="mt-2 text-red-700">
                {error.message || 'An unexpected error occurred'}
              </p>
            </div>
            <Button color="red" onClick={resetError}>
              Try Again
            </Button>
          </div>
        </Alert>
      </div>
    </div>
  );
}
