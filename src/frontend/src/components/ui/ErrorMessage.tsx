import React from 'react';
import { Alert } from './alert';
import { Button } from './button';
import { getErrorMessage, isNetworkError, isAuthError } from '../../utils/apiHelpers';

interface ErrorMessageProps {
  error: unknown;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  title?: string;
  showDetails?: boolean;
}

/**
 * Error message component for displaying API and other errors
 * Provides different styling and actions based on error type
 */
export function ErrorMessage({
  error,
  onRetry,
  onDismiss,
  className,
  title,
  showDetails = false,
}: ErrorMessageProps) {
  const errorMessage = getErrorMessage(error);
  const isNetwork = isNetworkError(error);
  const isAuth = isAuthError(error);

  // Determine error type and styling
  const getErrorConfig = () => {
    if (isAuth) {
      return {
        color: 'amber' as const,
        title: title || 'Authentication Error',
        message: 'Please sign in again to continue.',
        showRetry: false,
      };
    }

    if (isNetwork) {
      return {
        color: 'red' as const,
        title: title || 'Connection Error',
        message: 'Unable to connect to the server. Please check your internet connection.',
        showRetry: true,
      };
    }

    return {
      color: 'red' as const,
      title: title || 'Error',
      message: errorMessage,
      showRetry: true,
    };
  };

  const config = getErrorConfig();

  return (
    <Alert color={config.color} className={className}>
      <div className="space-y-3">
        <div>
          <h4 className="font-semibold">{config.title}</h4>
          <p className="mt-1 text-sm">{config.message}</p>
        </div>

        {showDetails && error && typeof error === 'object' && 'details' in error && (
          <details className="text-sm">
            <summary className="cursor-pointer font-medium">
              Error Details
            </summary>
            <div className="mt-2 p-3 bg-gray-50 rounded border">
              <pre className="text-xs whitespace-pre-wrap">
                {JSON.stringify(error, null, 2)}
              </pre>
            </div>
          </details>
        )}

        <div className="flex space-x-3">
          {config.showRetry && onRetry && (
            <Button size="sm" color={config.color} onClick={onRetry}>
              Try Again
            </Button>
          )}
          {onDismiss && (
            <Button size="sm" outline onClick={onDismiss}>
              Dismiss
            </Button>
          )}
        </div>
      </div>
    </Alert>
  );
}

/**
 * Inline error message for form fields and smaller components
 */
interface InlineErrorProps {
  error?: string | null;
  className?: string;
}

export function InlineError({ error, className }: InlineErrorProps) {
  if (!error) return null;

  return (
    <p className={`text-sm text-red-600 mt-1 ${className || ''}`}>
      {error}
    </p>
  );
}

/**
 * Error state component for empty states with errors
 */
interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorState({
  title = 'Something went wrong',
  message = 'An error occurred while loading this content.',
  onRetry,
  className,
}: ErrorStateProps) {
  return (
    <div className={`text-center py-12 ${className || ''}`}>
      <div className="mx-auto w-12 h-12 text-red-400 mb-4">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-6">{message}</p>
      {onRetry && (
        <Button color="indigo" onClick={onRetry}>
          Try Again
        </Button>
      )}
    </div>
  );
}
