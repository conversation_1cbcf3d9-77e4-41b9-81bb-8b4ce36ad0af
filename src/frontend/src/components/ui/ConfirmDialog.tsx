import React from 'react';
import { Dialog, DialogActions, DialogBody, DialogDescription, DialogTitle } from './dialog';
import { Button } from './button';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  variant?: 'danger' | 'warning' | 'info';
  isLoading?: boolean;
}

/**
 * Confirmation dialog component for destructive or important actions
 * Uses Catalyst Dialog components with consistent styling
 */
export function ConfirmDialog({
  open,
  onClose,
  onConfirm,
  title,
  description,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  variant = 'info',
  isLoading = false,
}: ConfirmDialogProps) {
  const getVariantConfig = () => {
    switch (variant) {
      case 'danger':
        return {
          confirmColor: 'red' as const,
          icon: (
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          ),
        };
      case 'warning':
        return {
          confirmColor: 'amber' as const,
          icon: (
            <svg className="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          ),
        };
      default:
        return {
          confirmColor: 'indigo' as const,
          icon: (
            <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
        };
    }
  };

  const config = getVariantConfig();

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogBody>
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            {config.icon}
          </div>
          <div className="flex-1">
            <DialogTitle>{title}</DialogTitle>
            {description && (
              <DialogDescription className="mt-2">
                {description}
              </DialogDescription>
            )}
          </div>
        </div>
      </DialogBody>
      <DialogActions>
        <Button 
          outline 
          onClick={onClose}
          disabled={isLoading}
        >
          {cancelLabel}
        </Button>
        <Button 
          color={config.confirmColor}
          onClick={onConfirm}
          disabled={isLoading}
        >
          {isLoading ? 'Processing...' : confirmLabel}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

/**
 * Hook for managing confirmation dialogs
 */
export function useConfirmDialog() {
  const [isOpen, setIsOpen] = React.useState(false);
  const [config, setConfig] = React.useState<Omit<ConfirmDialogProps, 'open' | 'onClose' | 'onConfirm'>>({
    title: '',
  });
  const [onConfirmCallback, setOnConfirmCallback] = React.useState<(() => void) | null>(null);

  const confirm = React.useCallback((
    options: Omit<ConfirmDialogProps, 'open' | 'onClose' | 'onConfirm'>
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfig(options);
      setOnConfirmCallback(() => () => {
        resolve(true);
        setIsOpen(false);
      });
      setIsOpen(true);
    });
  }, []);

  const handleClose = React.useCallback(() => {
    setIsOpen(false);
    setOnConfirmCallback(null);
  }, []);

  const handleConfirm = React.useCallback(() => {
    if (onConfirmCallback) {
      onConfirmCallback();
    }
  }, [onConfirmCallback]);

  const ConfirmDialogComponent = React.useCallback(() => (
    <ConfirmDialog
      {...config}
      open={isOpen}
      onClose={handleClose}
      onConfirm={handleConfirm}
    />
  ), [config, isOpen, handleClose, handleConfirm]);

  return {
    confirm,
    ConfirmDialog: ConfirmDialogComponent,
  };
}
