import React from 'react';
import { Descope } from '@descope/react-sdk';
import { useAuth } from '../../hooks/useAuth';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireRole?: string;
  requirePermission?: string;
}

/**
 * AuthGuard component that protects routes and components
 * Shows Descope login if not authenticated, loading spinner while checking auth,
 * and optionally checks for specific roles or permissions
 */
export function AuthGuard({ 
  children, 
  fallback,
  requireRole,
  requirePermission 
}: AuthGuardProps) {
  const { isAuthenticated, isLoading, user } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Show Descope login if not authenticated
  if (!isAuthenticated) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              Welcome to Shevet
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Please sign in to continue
            </p>
          </div>
          <div className="mt-8">
            <Descope
              flowId="sign-in-otp-or-social"
              onSuccess={(e) => {
                console.log('Login successful:', e.detail.user);
              }}
              onError={(e) => {
                console.error('Login error:', e);
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  // Check role requirement
  if (requireRole && user?.role !== requireRole) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Access Denied
          </h2>
          <p className="text-gray-600">
            You don't have the required role ({requireRole}) to access this page.
          </p>
        </div>
      </div>
    );
  }

  // Check permission requirement
  if (requirePermission && !user?.permissions?.includes(requirePermission)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Access Denied
          </h2>
          <p className="text-gray-600">
            You don't have the required permission ({requirePermission}) to access this page.
          </p>
        </div>
      </div>
    );
  }

  // User is authenticated and has required permissions
  return <>{children}</>;
}

/**
 * Higher-order component version of AuthGuard
 */
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requireRole?: string;
    requirePermission?: string;
  }
) {
  return function AuthGuardedComponent(props: P) {
    return (
      <AuthGuard 
        requireRole={options?.requireRole}
        requirePermission={options?.requirePermission}
      >
        <Component {...props} />
      </AuthGuard>
    );
  };
}
