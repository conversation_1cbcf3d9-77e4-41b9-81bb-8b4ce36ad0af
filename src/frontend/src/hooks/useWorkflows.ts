import { useState, useEffect, useCallback } from 'react';
import { Workflow, WorkflowStatus } from '../types/api';

// Mock data for development - will be replaced with actual API calls
const mockWorkflows: Workflow[] = [
  {
    id: '1',
    name: 'Lead Qualification Workflow',
    description: 'Automatically qualify incoming leads based on predefined criteria',
    status: 'active',
    isActive: true,
    version: 2,
    configuration: {},
    ownerId: 'user1',
    owner: {
      id: 'user1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'admin',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    lastRunAt: '2024-01-15T10:30:00Z',
    totalRuns: 156,
    successfulRuns: 142,
    failedRuns: 14,
    tags: ['sales', 'automation', 'leads'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: 'Customer Onboarding',
    description: 'Guide new customers through the onboarding process',
    status: 'active',
    isActive: true,
    version: 1,
    configuration: {},
    ownerId: 'user2',
    owner: {
      id: 'user2',
      name: 'Jane <PERSON>',
      email: '<EMAIL>',
      role: 'user',
      isActive: true,
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
    },
    lastRunAt: '2024-01-14T15:45:00Z',
    totalRuns: 89,
    successfulRuns: 87,
    failedRuns: 2,
    tags: ['onboarding', 'customer-success'],
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-14T15:45:00Z',
  },
  {
    id: '3',
    name: 'Support Ticket Routing',
    description: 'Automatically route support tickets to appropriate teams',
    status: 'paused',
    isActive: false,
    version: 3,
    configuration: {},
    ownerId: 'user1',
    owner: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'admin',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    lastRunAt: '2024-01-10T09:15:00Z',
    totalRuns: 234,
    successfulRuns: 220,
    failedRuns: 14,
    tags: ['support', 'routing', 'automation'],
    createdAt: '2023-12-15T00:00:00Z',
    updatedAt: '2024-01-10T09:15:00Z',
  },
  {
    id: '4',
    name: 'Weekly Report Generation',
    description: 'Generate and distribute weekly performance reports',
    status: 'draft',
    isActive: false,
    version: 1,
    configuration: {},
    ownerId: 'user2',
    owner: {
      id: 'user2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'user',
      isActive: true,
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
    },
    totalRuns: 0,
    successfulRuns: 0,
    failedRuns: 0,
    tags: ['reporting', 'analytics'],
    createdAt: '2024-01-16T00:00:00Z',
    updatedAt: '2024-01-16T00:00:00Z',
  },
];

export interface WorkflowFilters {
  search?: string;
  status?: WorkflowStatus | 'all';
  owner?: string;
  tags?: string[];
}

export interface WorkflowSortConfig {
  field: keyof Workflow;
  direction: 'asc' | 'desc';
}

export interface UseWorkflowsReturn {
  workflows: Workflow[];
  loading: boolean;
  error: string | null;
  filters: WorkflowFilters;
  sortConfig: WorkflowSortConfig;
  setFilters: (filters: Partial<WorkflowFilters>) => void;
  setSortConfig: (config: WorkflowSortConfig) => void;
  createWorkflow: (workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateWorkflow: (id: string, updates: Partial<Workflow>) => Promise<void>;
  deleteWorkflow: (id: string) => Promise<void>;
  duplicateWorkflow: (id: string) => Promise<void>;
  toggleWorkflowStatus: (id: string) => Promise<void>;
  refreshWorkflows: () => Promise<void>;
}

export function useWorkflows(): UseWorkflowsReturn {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFiltersState] = useState<WorkflowFilters>({
    search: '',
    status: 'all',
    owner: '',
    tags: [],
  });
  const [sortConfig, setSortConfigState] = useState<WorkflowSortConfig>({
    field: 'updatedAt',
    direction: 'desc',
  });

  // Filter and sort workflows
  const filteredAndSortedWorkflows = useCallback(() => {
    let filtered = [...mockWorkflows];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        (workflow) =>
          workflow.name.toLowerCase().includes(searchLower) ||
          workflow.description?.toLowerCase().includes(searchLower) ||
          workflow.tags?.some((tag) => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter((workflow) => workflow.status === filters.status);
    }

    // Apply owner filter
    if (filters.owner) {
      filtered = filtered.filter((workflow) => workflow.ownerId === filters.owner);
    }

    // Apply tags filter
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter((workflow) =>
        filters.tags!.some((tag) => workflow.tags?.includes(tag))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];

      if (aValue === undefined || aValue === null) return 1;
      if (bValue === undefined || bValue === null) return -1;

      let comparison = 0;
      if (aValue < bValue) comparison = -1;
      if (aValue > bValue) comparison = 1;

      return sortConfig.direction === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [filters, sortConfig]);

  // Load workflows (simulate API call)
  const loadWorkflows = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));
      setWorkflows(filteredAndSortedWorkflows());
    } catch (err) {
      setError('Failed to load workflows');
    } finally {
      setLoading(false);
    }
  }, [filteredAndSortedWorkflows]);

  // Update workflows when filters or sort config changes
  useEffect(() => {
    setWorkflows(filteredAndSortedWorkflows());
  }, [filteredAndSortedWorkflows]);

  // Initial load
  useEffect(() => {
    loadWorkflows();
  }, [loadWorkflows]);

  const setFilters = useCallback((newFilters: Partial<WorkflowFilters>) => {
    setFiltersState((prev) => ({ ...prev, ...newFilters }));
  }, []);

  const setSortConfig = useCallback((config: WorkflowSortConfig) => {
    setSortConfigState(config);
  }, []);

  const createWorkflow = useCallback(async (workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>) => {
    // TODO: Implement API call
    console.log('Creating workflow:', workflow);
  }, []);

  const updateWorkflow = useCallback(async (id: string, updates: Partial<Workflow>) => {
    // TODO: Implement API call
    console.log('Updating workflow:', id, updates);
  }, []);

  const deleteWorkflow = useCallback(async (id: string) => {
    // TODO: Implement API call
    console.log('Deleting workflow:', id);
  }, []);

  const duplicateWorkflow = useCallback(async (id: string) => {
    // TODO: Implement API call
    console.log('Duplicating workflow:', id);
  }, []);

  const toggleWorkflowStatus = useCallback(async (id: string) => {
    // TODO: Implement API call
    console.log('Toggling workflow status:', id);
  }, []);

  const refreshWorkflows = useCallback(async () => {
    await loadWorkflows();
  }, [loadWorkflows]);

  return {
    workflows,
    loading,
    error,
    filters,
    sortConfig,
    setFilters,
    setSortConfig,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    duplicateWorkflow,
    toggleWorkflowStatus,
    refreshWorkflows,
  };
}
