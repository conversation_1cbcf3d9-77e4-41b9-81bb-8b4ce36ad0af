import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { ApiService } from '../services/api';
import type { ApiResponse, ApiError, PaginatedResponse } from '../types/api';

/**
 * Query key factory for consistent cache keys
 */
export const queryKeys = {
  all: ['api'] as const,
  users: () => [...queryKeys.all, 'users'] as const,
  user: (id: string) => [...queryKeys.users(), id] as const,
  workflows: () => [...queryKeys.all, 'workflows'] as const,
  workflow: (id: string) => [...queryKeys.workflows(), id] as const,
  opportunities: () => [...queryKeys.all, 'opportunities'] as const,
  opportunity: (id: string) => [...queryKeys.opportunities(), id] as const,
  tasks: () => [...queryKeys.all, 'tasks'] as const,
  task: (id: string) => [...queryKeys.tasks(), id] as const,
};

/**
 * Generic hook for GET requests
 */
export function useApiQuery<T>(
  queryKey: readonly unknown[],
  url: string,
  options?: Omit<UseQueryOptions<ApiResponse<T>, ApiError>, 'queryKey' | 'queryFn'>
) {
  return useQuery<ApiResponse<T>, ApiError>({
    queryKey,
    queryFn: () => ApiService.get<T>(url),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors (client errors)
      if (error.code && typeof error.code === 'number' && error.code >= 400 && error.code < 500) {
        return false;
      }
      return failureCount < 3;
    },
    ...options,
  });
}

/**
 * Generic hook for POST mutations
 */
export function useApiMutation<TData, TVariables = any>(
  url: string,
  options?: Omit<UseMutationOptions<ApiResponse<TData>, ApiError, TVariables>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<TData>, ApiError, TVariables>({
    mutationFn: (variables: TVariables) => ApiService.post<TData>(url, variables),
    onSuccess: (data, variables, context) => {
      // Invalidate relevant queries on successful mutation
      queryClient.invalidateQueries({ queryKey: queryKeys.all });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Generic hook for PUT mutations
 */
export function useApiUpdateMutation<TData, TVariables = any>(
  url: string,
  options?: Omit<UseMutationOptions<ApiResponse<TData>, ApiError, TVariables>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<TData>, ApiError, TVariables>({
    mutationFn: (variables: TVariables) => ApiService.put<TData>(url, variables),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.all });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Generic hook for DELETE mutations
 */
export function useApiDeleteMutation<TData = any>(
  url: string,
  options?: Omit<UseMutationOptions<ApiResponse<TData>, ApiError, void>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<TData>, ApiError, void>({
    mutationFn: () => ApiService.delete<TData>(url),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.all });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook for paginated queries
 */
export function usePaginatedQuery<T>(
  queryKey: readonly unknown[],
  url: string,
  page: number = 1,
  limit: number = 10,
  options?: Omit<UseQueryOptions<PaginatedResponse<T>, ApiError>, 'queryKey' | 'queryFn'>
) {
  const paginatedUrl = `${url}?page=${page}&limit=${limit}`;
  
  return useQuery<PaginatedResponse<T>, ApiError>({
    queryKey: [...queryKey, 'paginated', page, limit],
    queryFn: () => ApiService.get<T[]>(paginatedUrl),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    keepPreviousData: true, // Keep previous page data while loading new page
    ...options,
  });
}

/**
 * Hook to invalidate specific queries
 */
export function useInvalidateQueries() {
  const queryClient = useQueryClient();

  const invalidateAll = () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.all });
  };

  const invalidateUsers = () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.users() });
  };

  const invalidateWorkflows = () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.workflows() });
  };

  const invalidateOpportunities = () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.opportunities() });
  };

  const invalidateTasks = () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.tasks() });
  };

  return {
    invalidateAll,
    invalidateUsers,
    invalidateWorkflows,
    invalidateOpportunities,
    invalidateTasks,
  };
}
