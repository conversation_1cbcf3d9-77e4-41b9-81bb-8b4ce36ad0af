import { useCallback, useMemo } from 'react';
import { useDescope, useSession, useUser } from '@descope/react-sdk';
import { AuthService, type AppUser, type AuthState } from '../services/auth';

/**
 * Custom authentication hook that wraps Descope hooks
 * Provides a unified interface for authentication state and actions
 */
export function useAuth(): AuthState & {
  login: () => void;
  logout: () => void;
  refresh: () => void;
} {
  const { isAuthenticated, isSessionLoading } = useSession();
  const { user, isUserLoading } = useUser();
  const { logout: descopeLogout } = useDescope();

  // Transform Descope user to our AppUser interface
  const appUser: AppUser | null = useMemo(() => {
    if (!user) return null;
    
    return {
      id: user.userId || user.loginId || '',
      name: user.name || user.givenName || user.email || 'Unknown User',
      email: user.email || '',
      // You can extend this to include role information from user custom attributes
      role: user.customAttributes?.role as any || 'user',
      permissions: user.customAttributes?.permissions as string[] || [],
    };
  }, [user]);

  // Get current token
  const token = useMemo(() => {
    return AuthService.getToken();
  }, [isAuthenticated]);

  // Logout function
  const logout = useCallback(() => {
    try {
      descopeLogout();
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }, [descopeLogout]);

  // Login function (for programmatic login if needed)
  const login = useCallback(() => {
    // Descope handles login through the Descope component
    // This is here for consistency but actual login is handled by Descope UI
    console.log('Login should be handled by Descope component');
  }, []);

  // Refresh function (for token refresh if needed)
  const refresh = useCallback(() => {
    // Descope handles token refresh automatically
    // This is here for consistency
    console.log('Token refresh is handled automatically by Descope');
  }, []);

  return {
    isAuthenticated: isAuthenticated && !!appUser,
    isLoading: isSessionLoading || isUserLoading,
    user: appUser,
    token,
    login,
    logout,
    refresh,
  };
}

/**
 * Hook for checking user permissions
 */
export function usePermissions() {
  const { user } = useAuth();

  const hasPermission = useCallback((permission: string): boolean => {
    if (!user?.permissions) return false;
    return user.permissions.includes(permission);
  }, [user?.permissions]);

  const hasRole = useCallback((role: string): boolean => {
    if (!user?.role) return false;
    return user.role === role;
  }, [user?.role]);

  const hasAnyRole = useCallback((roles: string[]): boolean => {
    if (!user?.role) return false;
    return roles.includes(user.role);
  }, [user?.role]);

  return {
    hasPermission,
    hasRole,
    hasAnyRole,
    userRole: user?.role,
    userPermissions: user?.permissions || [],
  };
}
