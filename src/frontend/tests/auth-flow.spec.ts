import { test, expect } from '@playwright/test';

// Override the global setup for these tests
test.use({ storageState: undefined });

test.describe('Authentication Flow', () => {
  test('should show login page for unauthenticated users', async ({ page }) => {
    await page.goto('http://localhost:4173');

    // Wait for the page to load
    await page.waitForTimeout(5000);

    // Should show the welcome message
    await expect(page.locator('text=Welcome to Shevet')).toBeVisible();

    // Should show the sign in prompt
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();

    // Should show the Descope authentication component
    // Note: The actual Descope form might be in an iframe or shadow DOM
    // For now, we'll just check that the page loaded without errors

    // Take a screenshot for verification
    await page.screenshot({ path: 'tests/screenshots/login-page.png' });
  });

  test('should handle authentication with test user', async ({ page }) => {
    await page.goto('http://localhost:4173');
    
    // Wait for the page to load
    await page.waitForTimeout(3000);
    
    // Look for email input field (this might be inside Descope component)
    // Note: Since Descope uses its own components, we might need to wait for them to load
    await page.waitForTimeout(5000);
    
    // Try to find and fill the email field
    // This is a basic test - the actual Descope integration might require more specific selectors
    try {
      const emailInput = page.locator('input[type="email"], input[placeholder*="email" i]').first();
      if (await emailInput.isVisible({ timeout: 5000 })) {
        await emailInput.fill('<EMAIL>');
        
        // Look for OTP input or continue button
        const continueButton = page.locator('button:has-text("Continue"), button:has-text("Send"), button[type="submit"]').first();
        if (await continueButton.isVisible({ timeout: 3000 })) {
          await continueButton.click();
          
          // Wait for OTP field
          await page.waitForTimeout(2000);
          
          // Try to find OTP input
          const otpInput = page.locator('input[type="text"], input[placeholder*="code" i], input[placeholder*="otp" i]').first();
          if (await otpInput.isVisible({ timeout: 5000 })) {
            await otpInput.fill('123456');
            
            // Submit OTP
            const submitButton = page.locator('button:has-text("Verify"), button:has-text("Submit"), button[type="submit"]').first();
            if (await submitButton.isVisible({ timeout: 3000 })) {
              await submitButton.click();
              
              // Wait for authentication to complete
              await page.waitForTimeout(5000);
              
              // Check if we're redirected to the dashboard
              await expect(page.locator('text=Dashboard')).toBeVisible({ timeout: 10000 });
            }
          }
        }
      }
    } catch (error) {
      console.log('Authentication test skipped - Descope form not accessible:', error);
      // Take a screenshot for debugging
      await page.screenshot({ path: 'tests/screenshots/auth-test-debug.png' });
    }
  });

  test('should show loading state initially', async ({ page }) => {
    await page.goto('http://localhost:4173');
    
    // Should show loading state briefly
    const loadingText = page.locator('text=Loading...');
    
    // Wait for either loading to appear or the main content
    await Promise.race([
      loadingText.waitFor({ timeout: 2000 }).catch(() => {}),
      page.locator('text=Welcome to Shevet').waitFor({ timeout: 5000 })
    ]);
    
    // Take a screenshot
    await page.screenshot({ path: 'tests/screenshots/initial-load.png' });
  });

  test('should have proper page title and meta tags', async ({ page }) => {
    await page.goto('http://localhost:4173');
    
    // Check page title
    await expect(page).toHaveTitle(/Shevet|Vite/);
    
    // Check that the page loads without JavaScript errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.waitForTimeout(5000);
    
    // Filter out known external service errors (like Descope CDN)
    const criticalErrors = errors.filter(error => 
      !error.includes('503') && 
      !error.includes('descope') && 
      !error.includes('cdn')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });
});
