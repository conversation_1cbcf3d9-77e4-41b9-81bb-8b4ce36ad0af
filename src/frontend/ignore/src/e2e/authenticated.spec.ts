import { test, expect } from '@playwright/test';

test.describe('Authenticated User Tests', () => {
  test('should be authenticated and show dashboard', async ({ page }) => {
    console.log('🔐 Testing authenticated user state...');
    
    // Navigate to the app (should be authenticated due to storage state)
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of authenticated state
    await page.screenshot({ 
      path: 'test-results/authenticated-dashboard.png',
      fullPage: true 
    });
    
    // Verify we're authenticated and see the dashboard
    await expect(page.locator('text=You are successfully authenticated with <PERSON><PERSON>.')).toBeVisible();
    
    // Verify user welcome message (user name from test account)
    await expect(page.locator('text=Welcome, Test User1!')).toBeVisible();
    
    // Verify user info section is present
    await expect(page.locator('text=User Info:')).toBeVisible();
    await expect(page.locator('text=Email: <EMAIL>')).toBeVisible();
    
    // Verify logout button is present
    await expect(page.locator('button:has-text("Sign Out")')).toBeVisible();
    
    console.log('✅ Authenticated state verified successfully');
  });

  test('should be able to logout', async ({ page }) => {
    console.log('🚪 Testing logout functionality...');
    
    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify we're authenticated first
    await expect(page.locator('text=You are successfully authenticated with Shevet.')).toBeVisible();
    
    // Click logout button
    const logoutButton = page.locator('button:has-text("Sign Out")');
    await logoutButton.click();
    
    // Wait for logout to complete
    await page.waitForTimeout(2000);
    
    // Should now see login form
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    await expect(page.locator('descope-wc')).toBeVisible();
    
    // Take screenshot of logout state
    await page.screenshot({ 
      path: 'test-results/after-logout.png',
      fullPage: true 
    });
    
    console.log('✅ Logout functionality verified successfully');
  });

  test('should maintain authentication across page reloads', async ({ page }) => {
    console.log('🔄 Testing authentication persistence...');
    
    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify we're authenticated
    await expect(page.locator('text=You are successfully authenticated with Shevet.')).toBeVisible();

    // Reload the page
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Should still be authenticated
    await expect(page.locator('text=You are successfully authenticated with Shevet.')).toBeVisible();
    
    console.log('✅ Authentication persistence verified');
  });

  test('should show correct user information', async ({ page }) => {
    console.log('👤 Testing user information display...');
    
    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify user information is displayed correctly
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    
    // Check if user ID is displayed
    const userIdElement = page.locator('text=User ID:').locator('..').locator('text=/U[a-zA-Z0-9]+/');
    await expect(userIdElement).toBeVisible();
    
    console.log('✅ User information display verified');
  });

  test('should handle navigation while authenticated', async ({ page }) => {
    console.log('🧭 Testing navigation while authenticated...');
    
    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify we're authenticated
    await expect(page.locator('text=You are successfully authenticated with Shevet.')).toBeVisible();

    // Try navigating to different paths (they should all show the dashboard since we don't have routing yet)
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('text=You are successfully authenticated with Shevet.')).toBeVisible();

    await page.goto('/profile');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('text=You are successfully authenticated with Shevet.')).toBeVisible();
    
    console.log('✅ Navigation while authenticated verified');
  });
});
