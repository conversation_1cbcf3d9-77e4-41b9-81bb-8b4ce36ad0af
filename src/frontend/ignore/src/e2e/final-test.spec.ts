import { test, expect } from '@playwright/test';

test.describe('Shevet Authentication Integration', () => {
  test('should demonstrate complete working authentication flow', async ({ page }) => {
    console.log('🎯 Final Integration Test - Complete Authentication Flow');
    console.log('');
    
    // Navigate to the app (should be authenticated due to auth setup)
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take screenshot of authenticated state
    await page.screenshot({ 
      path: 'test-results/final-authenticated-state.png',
      fullPage: true 
    });
    
    console.log('✅ Step 1: Application loaded');
    
    // Verify we're authenticated and see the dashboard
    await expect(page.locator('text=You are successfully authenticated with Shevet.')).toBeVisible();
    console.log('✅ Step 2: Authentication verified');
    
    // Verify user welcome message
    await expect(page.locator('text=Welcome, Test User1!')).toBeVisible();
    console.log('✅ Step 3: User welcome message displayed');
    
    // Verify user info section is present
    await expect(page.locator('text=User Info:')).toBeVisible();
    console.log('✅ Step 4: User info section displayed');
    
    // Verify email is shown
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    console.log('✅ Step 5: User email displayed');
    
    // Verify logout button is present and functional
    const logoutButton = page.locator('button:has-text("Sign Out")');
    await expect(logoutButton).toBeVisible();
    console.log('✅ Step 6: Logout button present');
    
    // Test logout functionality
    await logoutButton.click();
    await page.waitForTimeout(3000);
    
    // Should now see login form
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    await expect(page.locator('descope-wc')).toBeVisible();
    console.log('✅ Step 7: Logout successful - login form displayed');
    
    // Take screenshot of logout state
    await page.screenshot({ 
      path: 'test-results/final-logout-state.png',
      fullPage: true 
    });
    
    console.log('');
    console.log('🎉 AUTHENTICATION INTEGRATION TEST COMPLETE!');
    console.log('');
    console.log('📋 SUMMARY OF VERIFIED FUNCTIONALITY:');
    console.log('✅ Automated OTP authentication setup works');
    console.log('✅ User authentication state is properly managed');
    console.log('✅ Authenticated dashboard displays correctly');
    console.log('✅ User information (name, email) is shown');
    console.log('✅ Logout functionality works correctly');
    console.log('✅ Login form is displayed after logout');
    console.log('✅ Playwright MCP can test the complete flow');
    console.log('');
    console.log('🔧 TECHNICAL DETAILS:');
    console.log('• Auth method: Email + OTP (static OTP: 123456)');
    console.log('• Test user: <EMAIL>');
    console.log('• Flow ID: sign-in-otp-or-social');
    console.log('• Storage state: playwright/.auth/user.json');
    console.log('• Screenshots: test-results/final-*.png');
    console.log('');
    console.log('🚀 The application is ready for development with full authentication testing!');
  });
});
