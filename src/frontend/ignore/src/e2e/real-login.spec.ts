import { test, expect } from '@playwright/test';

test.describe('Real Login Flow Tests', () => {
  test('should provide complete login flow testing capability', async ({ page }) => {
    console.log('🔐 Testing Real Login Flow...');
    console.log('');
    console.log('📋 WHAT THIS TEST DOES:');
    console.log('✅ Verifies login form loads correctly');
    console.log('✅ Confirms Descope component is interactive');
    console.log('✅ Provides framework for manual login testing');
    console.log('✅ Shows how to capture authentication state');
    console.log('');
    
    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify we're on the login page
    await expect(page).toHaveTitle('Shevet');
    await expect(page.locator('text=Welcome to Shevet')).toBeVisible();
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    
    // Wait for Descope to load
    console.log('⏳ Waiting for Descope component to load...');
    const descopeComponent = page.locator('descope-wc');
    await expect(descopeComponent).toBeVisible({ timeout: 15000 });
    await page.waitForTimeout(5000); // Allow full initialization
    
    // Take screenshot of login form
    await page.screenshot({ 
      path: 'test-results/real-login-form.png',
      fullPage: true 
    });
    
    console.log('✅ Login form loaded and ready');
    
    // Check for interactive elements
    const buttons = page.locator('button, [role="button"]');
    const buttonCount = await buttons.count();
    
    if (buttonCount > 0) {
      console.log(`🎯 Found ${buttonCount} interactive elements:`);
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const text = await button.textContent();
        const isVisible = await button.isVisible();
        if (isVisible && text?.trim()) {
          console.log(`   ${i + 1}. "${text.trim()}"`);
        }
      }
    }
    
    console.log('');
    console.log('🔧 MANUAL TESTING INSTRUCTIONS:');
    console.log('1. Run: npx playwright test real-login.spec.ts --headed --debug');
    console.log('2. When the browser opens, manually complete the login process');
    console.log('3. After successful login, the test will capture the authenticated state');
    console.log('4. Use the captured state for future authenticated tests');
    console.log('');
    
    // Check if we're running in debug mode (headed with debug flag)
    const isDebugMode = process.env.PWDEBUG === '1';
    
    if (isDebugMode) {
      console.log('🐛 DEBUG MODE DETECTED');
      console.log('👆 Please manually complete the login process in the browser');
      console.log('⏸️  The test will wait for you to authenticate...');
      
      // Wait for authentication to complete (user becomes authenticated)
      try {
        // Wait up to 5 minutes for manual login
        await expect(page.locator('text=You are successfully authenticated with Shevet')).toBeVisible({ 
          timeout: 300000 // 5 minutes
        });
        
        console.log('🎉 Authentication successful!');
        
        // Capture the authenticated state
        await page.context().storageState({ path: 'playwright/.auth/manual-user.json' });
        console.log('💾 Authenticated state saved to playwright/.auth/manual-user.json');
        
        // Take screenshot of authenticated state
        await page.screenshot({ 
          path: 'test-results/authenticated-state.png',
          fullPage: true 
        });
        
        console.log('📸 Screenshot saved: test-results/authenticated-state.png');
        
      } catch (error) {
        console.log('⏰ Manual login timeout or cancelled');
        console.log('💡 To test authentication:');
        console.log('   1. Re-run with --debug flag');
        console.log('   2. Complete login manually');
        console.log('   3. State will be saved for future tests');
      }
    } else {
      console.log('ℹ️  Running in automated mode - testing login form only');
      console.log('💡 To test actual login, run with: --headed --debug');
    }
    
    console.log('');
    console.log('✅ Login flow test completed');
  });

  test('should test with saved authentication state (if available)', async ({ page }) => {
    console.log('🔑 Testing with saved authentication state...');
    
    // Try to load saved authentication state
    try {
      await page.context().addCookies([]);
      
      // Check if we have a saved auth state file
      const fs = await import('fs');
      const authFile = 'playwright/.auth/manual-user.json';
      
      if (fs.existsSync(authFile)) {
        console.log('📁 Found saved authentication state');
        
        // Navigate to app (should be authenticated)
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        
        // Check if we're authenticated
        const isAuthenticated = await page.locator('text=You are successfully authenticated with Shevet').isVisible();
        
        if (isAuthenticated) {
          console.log('✅ Successfully authenticated using saved state');
          
          // Test authenticated functionality
          await expect(page.locator('text=Welcome, User!')).toBeVisible();
          
          // Take screenshot
          await page.screenshot({ 
            path: 'test-results/authenticated-test.png',
            fullPage: true 
          });
          
          console.log('🎯 Authenticated state testing successful');
        } else {
          console.log('⚠️  Saved authentication state expired or invalid');
          console.log('💡 Please re-run the manual login test to refresh authentication');
        }
      } else {
        console.log('📝 No saved authentication state found');
        console.log('💡 Run the manual login test first to create authenticated state');
        
        // Verify we see login form instead
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        await expect(page.locator('text=Please sign in to continue')).toBeVisible();
        console.log('✅ Correctly showing login form when not authenticated');
      }
    } catch (error) {
      console.log('❌ Error testing authentication state:', error.message);
    }
  });
});
