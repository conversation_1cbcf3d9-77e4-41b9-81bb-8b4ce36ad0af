# Shevet Testing Guide

## Overview
This guide explains how to test the Shevet application with Descope authentication using Playwright MCP.

## Current Status ✅

### ✅ What's Working PERFECTLY
- **Automated OTP authentication** works with test user (<EMAIL>, OTP: 123456)
- **Complete authentication flow** tested end-to-end
- **User authentication state** properly managed and verified
- **Authenticated dashboard** displays correctly with user info
- **Logout functionality** works and returns to login form
- **Playwright MCP integration** provides full testing capabilities
- **Environment variables** properly configured for development and testing
- **Setup/teardown** handles authentication state automatically

### 🎯 Authentication Method
- **Flow**: Email + OTP (sign-in-otp-or-social)
- **Test User**: <EMAIL>
- **Static OTP**: 123456
- **Auto-setup**: Tests run with pre-authenticated state

## Test Structure

### 1. Basic Application Tests (`app.spec.ts`)
```bash
npx playwright test src/e2e/app.spec.ts
```
- Verifies application loads correctly
- Checks title, UI elements, and basic functionality
- Tests loading states and error handling

### 2. Login Flow Tests (`login-flow.spec.ts`)
```bash
npx playwright test src/e2e/login-flow.spec.ts
```
- Tests login form display and interaction
- Verifies Descope component loads with authentication options
- Tests navigation and routing protection

### 3. Authentication Tests (`manual-test.spec.ts`)
```bash
npx playwright test src/e2e/manual-test.spec.ts
```
- Verifies login form UI components
- Tests basic interaction capabilities
- Takes screenshots for visual verification

### 4. Authenticated User Tests (`authenticated.spec.ts`)
```bash
npx playwright test src/e2e/authenticated.spec.ts
```
- Tests authenticated user functionality
- Verifies user info display and logout
- Tests authentication persistence

### 5. Final Integration Test (`final-test.spec.ts`)
```bash
npx playwright test src/e2e/final-test.spec.ts
```
- Complete end-to-end authentication flow verification
- Demonstrates all working functionality
- Comprehensive test with detailed logging

## How Authentication Testing Works

### Automated Authentication Setup
The Playwright tests automatically authenticate using:

1. **Email**: <EMAIL>
2. **OTP**: 123456 (static OTP configured in Descope)
3. **Flow**: sign-in-otp-or-social

### Authentication Process
1. **Setup Phase**: Before tests run, auth.setup.ts:
   - Navigates to the application
   - Fills email field with test user email
   - Submits email and waits for OTP form
   - Fills OTP fields with static code
   - Verifies authentication success
   - Saves authentication state to `playwright/.auth/user.json`

2. **Test Phase**: Tests run with authenticated state:
   - All tests start with user already logged in
   - Can test authenticated functionality directly
   - No need to handle login in individual tests

3. **Teardown Phase**: After tests complete:
   - Cleans up authentication state file
   - Ensures clean state for next test run

### Manual Testing
```bash
# Run the application
npm run dev

# Navigate to http://localhost:4174
# Use test credentials:
# Email: <EMAIL>
# OTP: 123456
```

## Test Results and Screenshots

All tests generate screenshots in `test-results/`:
- `login-form-loaded.png` - Login form after Descope loads
- `authenticated-state.png` - Dashboard after successful login
- `integration-test-complete.png` - Full application state
- And more...

## Environment Configuration

Required environment variables in `.env`:
```
VITE_DESCOPE_PROJECT_ID=P2vKC8jjsUAuunFbeEFzZmkcbsbb
DESCOPE_MANAGEMENT_KEY=K2zrtiDb9fVjUKwNC6CVQhq9q3tXK8eX4DePpOJRMzQjuNxyg3JYvzHuED3CT29wgydKvIG
```

## Running All Tests

```bash
# Run all tests
npx playwright test

# Run with browser visible
npx playwright test --headed

# Run specific browser
npx playwright test --project=chromium

# Run with debug mode
npx playwright test --debug
```

## Summary

🎉 **COMPLETE SUCCESS!**

✅ **Automated OTP authentication** works perfectly with test user
✅ **Full authentication flow** tested end-to-end automatically
✅ **Playwright MCP integration** provides comprehensive testing capabilities
✅ **User authentication state** properly managed with setup/teardown
✅ **Authenticated functionality** fully testable (dashboard, logout, user info)
✅ **Login form testing** works for unauthenticated scenarios
✅ **Environment configuration** properly set up for development and testing

**The application is fully ready for development with complete authentication testing!**

### Key Achievement
- **Email + OTP flow** with static test user (<EMAIL>, OTP: 123456)
- **Automated authentication setup** eliminates manual login for tests
- **Future tests can focus on app functionality** instead of authentication
- **Complete test coverage** of authentication flow and authenticated features
