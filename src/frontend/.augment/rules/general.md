---
type: "always_apply"
---

# General rules
## General
- Use context7

## Frontend
- When working on UI, consider using playwright <PERSON><PERSON> to test the changes as you work on them
- When working on frontend, use exisiting components UNLESS:
  - It's absolutely necessary to build new components to achieve the objective
  - The user has explicitly asked to implement new components

## Backend
- When working on backend, focus on the following:
  - Work within the confines of the existing architecture
  - Adapt existing coding style
  - For testing
    - Use pytest instead of unittest
    - Use fixtures where possible to keep code clean and readable
  - Run ruff and ty in using `uvx` in the relevant directory to make sure the code style adheres to project rules